/* Custom testimonial card height for uniformity */
.testimonial-card {
  min-height: 420px;
  max-height: 420px;
  height: 420px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  /* Ensure card stretches to fill parent */
  width: 100%;
  flex: 1 1 auto;
}

.testimonial-card:hover, .testimonial-card:focus {
  /* Remove any hover effect */
  box-shadow: none !important;
  background: inherit !important;
  transform: none !important;
}

.swiper-wrapper {
  align-items: stretch !important;
  height: 100%;
}

.swiper-slide {
  height: 100%;
  display: flex;
  align-items: stretch;
}

.testimonial-card > .text-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@media (max-width: 640px) {
  .testimonial-card {
    min-height: 380px;
    max-height: 380px;
    height: 380px;
  }
}
